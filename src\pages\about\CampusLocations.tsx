
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MapPin } from 'lucide-react';

const CampusLocations = () => {
  const locations = [
    {
      name: "Main Campus",
      address: "123 University Avenue, Beirut",
      features: [
        "Modern academic buildings",
        "State-of-the-art research facilities",
        "Student center and recreation facilities",
        "Central library"
      ]
    },
    {
      name: "Innovation Campus",
      address: "456 Technology Park, Tripoli",
      features: [
        "Technology innovation center",
        "Engineering laboratories",
        "Business incubator",
        "Conference center"
      ]
    },
    {
      name: "Medical Campus",
      address: "789 Healthcare Drive, Sidon",
      features: [
        "Medical training facilities",
        "Research laboratories",
        "Simulation center",
        "Health sciences library"
      ]
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">Campus Locations</h1>
          
          <p className="text-lg text-gray-600 mb-8">
            AUL maintains multiple campus locations across Lebanon, each offering specialized 
            facilities and unique learning environments for our students.
          </p>

          <div className="grid md:grid-cols-3 gap-8">
            {locations.map((location, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="text-aul-gold" />
                    {location.name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{location.address}</p>
                  <h3 className="font-semibold mb-2">Key Features:</h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-600">
                    {location.features.map((feature, idx) => (
                      <li key={idx}>{feature}</li>
                    ))}
                  </ul>
                  <div className="mt-4">
                    <Button variant="outline" className="w-full">
                      View Campus Map
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default CampusLocations;
