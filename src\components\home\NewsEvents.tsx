import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, Clock, ArrowRight, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Badge } from "@/components/ui/badge";

const newsItems = [
  {
    id: 1,
    title: "AUL Launches New Computer Science Program",
    excerpt: "The Faculty of Sciences introduces a cutting-edge program focused on AI and machine learning.",
    date: "April 15, 2025",
    image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?auto=format&fit=crop&q=80&w=600",
    category: "Academics",
    url: "/news-events/news/new-cs-program"
  },
  {
    id: 2,
    title: "AUL Students Win International Research Competition",
    excerpt: "Engineering students showcase innovative renewable energy solutions at global conference.",
    date: "April 10, 2025",
    image: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?auto=format&fit=crop&q=80&w=600",
    category: "Research",
    url: "/news-events/news/research-competition"
  },
  {
    id: 3,
    title: "AUL Signs Partnership with Microsoft for Student Opportunities",
    excerpt: "New collaboration will provide internships and career pathways for technology students.",
    date: "April 5, 2025",
    image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&q=80&w=600",
    category: "Partnerships",
    url: "/news-events/news/microsoft-partnership"
  }
];

const eventsItems = [
  {
    id: 1,
    title: "Annual Scientific Conference",
    description: "Presenting the latest research findings from AUL faculty and international scholars.",
    date: "May 15, 2025",
    time: "9:00 AM - 5:00 PM",
    location: "AUL Conference Center",
    url: "/news-events/events/scientific-conference"
  },
  {
    id: 2,
    title: "Spring Career Fair",
    description: "Connect with over 50 employers offering jobs and internships across various industries.",
    date: "May 10, 2025",
    time: "10:00 AM - 3:00 PM",
    location: "AUL Main Campus",
    url: "/news-events/events/career-fair"
  },
  {
    id: 3,
    title: "Cultural Festival",
    description: "Celebrating the diverse cultural heritage of our student community with performances, food, and art.",
    date: "May 5, 2025",
    time: "11:00 AM - 8:00 PM",
    location: "AUL Courtyard",
    url: "/news-events/events/cultural-festival"
  }
];

const NewsEvents = () => {
  return (
    <section className="section-padding bg-gray-50">
      <div className="aul-container">
        <div className="flex flex-col md:flex-row justify-between items-center mb-8">
          <h2 className="text-3xl font-bold text-aul-navy mb-4 md:mb-0">News & Events</h2>
          <div className="w-full md:w-auto">
            <Tabs defaultValue="news" className="w-full">
              <TabsList className="w-full md:w-auto grid grid-cols-2 mb-6">
                <TabsTrigger value="news" className="text-base">Latest News</TabsTrigger>
                <TabsTrigger value="events" className="text-base">Upcoming Events</TabsTrigger>
              </TabsList>
              
              <TabsContent value="news" className="mt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {newsItems.map((item) => (
                    <Card key={item.id} className="overflow-hidden card-hover h-full flex flex-col bg-white transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                      <div className="aspect-video overflow-hidden">
                        <img 
                          src={item.image} 
                          alt={item.title} 
                          className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                        />
                      </div>
                      <CardHeader>
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="secondary" className="bg-aul-gold/10 text-aul-navy hover:bg-aul-gold/20">
                            {item.category}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {item.date}
                          </span>
                        </div>
                        <CardTitle className="text-xl line-clamp-2 hover:text-aul-navy transition-colors">
                          {item.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="flex-grow">
                        <p className="text-gray-600 line-clamp-3">{item.excerpt}</p>
                      </CardContent>
                      <CardFooter>
                        <Link 
                          to={item.url}
                          className="text-aul-navy font-medium flex items-center hover:text-blue-700 transition-colors"
                        >
                          Read More <ArrowRight size={16} className="ml-1" />
                        </Link>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
                <div className="mt-8 text-center">
                  <Button variant="outline" asChild className="min-w-[150px]">
                    <Link to="/news-events/news">View All News</Link>
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="events" className="mt-0">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {eventsItems.map((event) => (
                    <Card key={event.id} className="card-hover flex flex-col h-full bg-white transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                      <CardHeader>
                        <CardTitle className="text-xl hover:text-aul-navy transition-colors">
                          {event.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="flex-grow">
                        <CardDescription className="text-base mb-4">
                          {event.description}
                        </CardDescription>
                        <div className="space-y-3">
                          <div className="flex items-start text-gray-600">
                            <Calendar className="h-5 w-5 text-aul-gold mr-2 mt-0.5 flex-shrink-0" />
                            <span>{event.date}</span>
                          </div>
                          <div className="flex items-start text-gray-600">
                            <Clock className="h-5 w-5 text-aul-gold mr-2 mt-0.5 flex-shrink-0" />
                            <span>{event.time}</span>
                          </div>
                          <div className="flex items-start text-gray-600">
                            <MapPin className="h-5 w-5 text-aul-gold mr-2 mt-0.5 flex-shrink-0" />
                            <span>{event.location}</span>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Link 
                          to={event.url}
                          className="text-aul-navy font-medium flex items-center hover:text-blue-700 transition-colors"
                        >
                          Event Details <ArrowRight size={16} className="ml-1" />
                        </Link>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
                <div className="mt-8 text-center">
                  <Button variant="outline" asChild className="min-w-[150px]">
                    <Link to="/news-events/events">View All Events</Link>
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsEvents;
