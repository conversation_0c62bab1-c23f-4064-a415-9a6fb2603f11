
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const CivilEngineering = () => {
  const programData = {
    name: "Bachelor of Engineering in Civil Engineering",
    description: "Our Civil Engineering program focuses on the design, construction, and maintenance of the built environment. Students learn to solve complex engineering challenges while considering environmental and social impacts.",
    duration: "4 years",
    faculty: "Faculty of Engineering",
    degreeAwarded: "Bachelor of Engineering (BEng) in Civil Engineering",
    careerOpportunities: [
      "Structural Engineer",
      "Construction Manager",
      "Transportation Engineer",
      "Environmental Engineer",
      "Geotechnical Engineer",
      "Water Resources Engineer",
      "Urban Planner",
      "Project Manager"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Engineering Mechanics",
          "Materials Science",
          "Calculus for Engineers",
          "Engineering Drawing",
          "Introduction to Civil Engineering"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Structural Analysis",
          "Soil Mechanics",
          "Fluid Mechanics",
          "Surveying",
          "Engineering Mathematics"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Reinforced Concrete Design",
          "Steel Structure Design",
          "Highway Engineering",
          "Hydraulic Engineering",
          "Environmental Engineering"
        ]
      },
      {
        year: "Fourth Year",
        courses: [
          "Foundation Engineering",
          "Construction Management",
          "Transportation Planning",
          "Capstone Design Project",
          "Professional Ethics in Engineering"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Minimum GPA of 3.0",
      "Strong background in Mathematics and Physics",
      "Spatial reasoning skills",
      "English language proficiency",
      "Statement of purpose"
    ],
    programObjectives: [
      "Develop strong problem-solving and analytical skills for engineering challenges",
      "Master fundamental principles of civil engineering design",
      "Apply engineering knowledge to create sustainable infrastructure",
      "Use modern engineering tools and technologies effectively",
      "Understand professional and ethical responsibilities of engineers",
      "Prepare for careers in design, construction, and infrastructure management"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default CivilEngineering;
