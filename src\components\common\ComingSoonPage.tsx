
import React from 'react';
import { CalendarClock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

interface ComingSoonPageProps {
  title: string;
  description: string;
  estimatedDate?: string;
}

const ComingSoonPage = ({ title, description, estimatedDate }: ComingSoonPageProps) => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-12">
        <Card className="max-w-3xl mx-auto">
          <CardContent className="flex flex-col items-center py-12 text-center">
            <CalendarClock className="w-16 h-16 text-aul-gold mb-6" />
            <h1 className="text-4xl font-bold text-aul-navy mb-4">{title}</h1>
            <p className="text-xl text-gray-600 mb-6 max-w-xl">{description}</p>
            {estimatedDate && (
              <p className="text-sm text-gray-500">
                Expected launch: {estimatedDate}
              </p>
            )}
          </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
};

export default ComingSoonPage;
