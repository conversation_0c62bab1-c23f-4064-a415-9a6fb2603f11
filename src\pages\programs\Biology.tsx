
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Biology = () => {
  const programData = {
    name: "Bachelor of Science in Biology",
    description: "Our Biology program provides a comprehensive understanding of living organisms, from molecular and cellular levels to ecosystems. Students engage in hands-on laboratory work and field studies to develop strong research skills.",
    duration: "4 years",
    faculty: "Faculty of Sciences",
    degreeAwarded: "Bachelor of Science (BSc) in Biology",
    careerOpportunities: [
      "Research Scientist",
      "Laboratory Technician",
      "Environmental Consultant",
      "Healthcare Professional",
      "Biotechnology Specialist",
      "Conservation Biologist",
      "Science Educator",
      "Pharmaceutical Researcher"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Introduction to Biology",
          "Cell Biology",
          "Chemistry for Life Sciences",
          "Genetics Fundamentals",
          "Scientific Methodology"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Molecular Biology",
          "Ecology",
          "Microbiology",
          "Human Physiology",
          "Evolutionary Biology"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Biochemistry",
          "Plant Biology",
          "Animal Physiology",
          "Genomics and Proteomics",
          "Biostatistics"
        ]
      },
      {
        year: "Fourth Year",
        courses: [
          "Immunology",
          "Conservation Biology",
          "Biotechnology Applications",
          "Research Project",
          "Bioethics"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Minimum GPA of 3.0",
      "Strong background in Biology and Chemistry",
      "Laboratory experience (preferred)",
      "English language proficiency",
      "Statement of purpose"
    ],
    programObjectives: [
      "Develop deep understanding of biological principles and processes",
      "Master laboratory techniques and research methods",
      "Analyze biological data using statistical and computational tools",
      "Understand the interconnections between organisms and their environments",
      "Develop critical thinking and problem-solving skills for biological research",
      "Prepare for careers in research, healthcare, and biotechnology"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default Biology;
