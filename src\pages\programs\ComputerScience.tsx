
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const ComputerScience = () => {
  const programData = {
    name: "Bachelor of Science in Computer Science",
    description: "Our Computer Science program provides a comprehensive foundation in computing theory, software development, and cutting-edge technologies. Students gain hands-on experience through practical projects and industry collaborations.",
    duration: "4 years",
    faculty: "Faculty of Sciences",
    degreeAwarded: "Bachelor of Science (BSc) in Computer Science",
    careerOpportunities: [
      "Software Developer/Engineer",
      "Data Scientist",
      "Artificial Intelligence Engineer",
      "Cloud Solutions Architect",
      "DevOps Engineer",
      "Cybersecurity Specialist",
      "Machine Learning Engineer",
      "Full-Stack Developer"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Introduction to Programming",
          "Computer Systems Architecture",
          "Discrete Mathematics",
          "Calculus for Computing",
          "Digital Logic Design"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Data Structures and Algorithms",
          "Object-Oriented Programming",
          "Database Systems",
          "Web Development",
          "Operating Systems"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Software Engineering",
          "Artificial Intelligence",
          "Computer Networks",
          "Cloud Computing",
          "Machine Learning Fundamentals"
        ]
      },
      {
        year: "Fourth Year",
        courses: [
          "Cybersecurity",
          "Big Data Analytics",
          "Mobile App Development",
          "Final Year Project",
          "Professional Ethics in Computing"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Minimum GPA of 3.0",
      "Strong foundation in Mathematics",
      "Programming experience (preferred but not required)",
      "English language proficiency",
      "Letter of motivation"
    ],
    programObjectives: [
      "Develop strong problem-solving and analytical skills",
      "Master fundamental programming concepts and multiple programming languages",
      "Understand computer systems, networks, and architectures",
      "Learn to design and implement efficient software solutions",
      "Gain expertise in modern development tools and methodologies",
      "Develop skills in emerging technologies like AI and cloud computing"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default ComputerScience;
