
import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';

const CampusLife = () => {
  return (
    <section className="section-padding bg-white overflow-hidden">
      <div className="aul-container">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">Campus Life</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            At AUL, we believe that education extends beyond the classroom. Discover the vibrant and diverse 
            student experience that makes our university community special.
          </p>
        </div>

        {/* Grid layout for campus life highlights */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Student Clubs & Activities */}
          <div className="relative group overflow-hidden rounded-lg h-80">
            <img 
              src="https://images.unsplash.com/photo-1517022812141-23620dba5c23?auto=format&fit=crop&q=80" 
              alt="Student Clubs & Activities" 
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-aul-navy to-transparent opacity-80"></div>
            <div className="absolute bottom-0 left-0 p-6 text-white">
              <h3 className="text-xl font-bold mb-2">Student Clubs & Activities</h3>
              <p className="mb-4">Join one of our 50+ student clubs and organizations to pursue your interests.</p>
              <Link 
                to="/student-life/clubs"
                className="text-white bg-aul-gold hover:bg-yellow-400 transition-colors py-2 px-4 rounded inline-block"
              >
                Explore Clubs
              </Link>
            </div>
          </div>

          {/* Athletics & Recreation */}
          <div className="relative group overflow-hidden rounded-lg h-80">
            <img 
              src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&q=80" 
              alt="Athletics & Recreation" 
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-aul-navy to-transparent opacity-80"></div>
            <div className="absolute bottom-0 left-0 p-6 text-white">
              <h3 className="text-xl font-bold mb-2">Athletics & Recreation</h3>
              <p className="mb-4">Stay active with our competitive sports teams and recreational facilities.</p>
              <Link 
                to="/student-life/athletics"
                className="text-white bg-aul-gold hover:bg-yellow-400 transition-colors py-2 px-4 rounded inline-block"
              >
                View Sports
              </Link>
            </div>
          </div>

          {/* Housing & Dining */}
          <div className="relative group overflow-hidden rounded-lg h-80">
            <img 
              src="https://images.unsplash.com/photo-1487958449943-2429e8be8625?auto=format&fit=crop&q=80" 
              alt="Housing & Dining" 
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-aul-navy to-transparent opacity-80"></div>
            <div className="absolute bottom-0 left-0 p-6 text-white">
              <h3 className="text-xl font-bold mb-2">Housing & Dining</h3>
              <p className="mb-4">Explore our comfortable living options and diverse dining experiences.</p>
              <Link 
                to="/student-life/housing"
                className="text-white bg-aul-gold hover:bg-yellow-400 transition-colors py-2 px-4 rounded inline-block"
              >
                Learn More
              </Link>
            </div>
          </div>
        </div>

        {/* Video Section */}
        <div className="mt-16 bg-gray-100 rounded-xl overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            <div className="p-8 lg:p-12 flex flex-col justify-center">
              <h3 className="text-2xl md:text-3xl font-bold text-aul-navy mb-4">Experience AUL</h3>
              <p className="text-lg text-gray-600 mb-6">
                Take a virtual tour of our campus and discover what makes AUL a special place to learn, 
                grow, and thrive. From state-of-the-art facilities to vibrant community spaces, 
                explore all that our university has to offer.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button className="bg-aul-navy" asChild>
                  <Link to="/virtual-tour">Virtual Tour</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link to="/campus-map">Campus Map</Link>
                </Button>
              </div>
            </div>
            <div className="aspect-video lg:h-auto bg-gray-200 flex items-center justify-center relative">
              {/* This would be a real video in production */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-20 h-20 bg-aul-gold rounded-full flex items-center justify-center cursor-pointer hover:bg-yellow-400 transition-colors">
                  <svg 
                    className="w-8 h-8 text-white ml-1" 
                    fill="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path d="M8 5v14l11-7z" />
                  </svg>
                </div>
              </div>
              <img 
                src="https://images.unsplash.com/photo-1501854140801-50d01698950b?auto=format&fit=crop&q=80" 
                alt="Campus Video Thumbnail" 
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>

        <div className="mt-12 text-center">
          <Button variant="default" asChild>
            <Link to="/student-life">Explore Student Life</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CampusLife;
