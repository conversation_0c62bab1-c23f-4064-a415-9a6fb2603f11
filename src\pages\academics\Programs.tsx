
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const Programs = () => {
  const programs = [
    {
      faculty: "Sciences",
      programs: [
        { name: "Computer Science", path: "/programs/computer-science" },
        { name: "Biology", path: "/programs/biology" },
        { name: "Mathematics", path: "/programs/mathematics" },
        { name: "Chemistry", path: "/programs/chemistry" }
      ]
    },
    {
      faculty: "Engineering",
      programs: [
        { name: "Civil Engineering", path: "/programs/civil-engineering" },
        { name: "Electrical Engineering", path: "/programs/electrical-engineering" },
        { name: "Mechanical Engineering", path: "/programs/mechanical-engineering" },
        { name: "Computer Engineering", path: "/programs/computer-engineering" }
      ]
    },
    {
      faculty: "Business",
      programs: [
        { name: "Business Administration", path: "/programs/business-administration" },
        { name: "Finance", path: "/programs/finance" },
        { name: "Marketing", path: "/programs/marketing" },
        { name: "Accounting", path: "/programs/accounting" }
      ]
    },
    {
      faculty: "Arts",
      programs: [
        { name: "Graphic Design", path: "/programs/graphic-design" },
        { name: "Digital Media", path: "/programs/digital-media" },
        { name: "English Literature", path: "/programs/english-literature" },
        { name: "Psychology", path: "/programs/psychology" }
      ]
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">Academic Programs</h1>
          
          <div className="grid md:grid-cols-2 gap-8">
            {programs.map((faculty, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{faculty.faculty}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {faculty.programs.map((program, idx) => (
                      <Button key={idx} variant="outline" asChild className="w-full justify-start">
                        <Link to={program.path}>{program.name}</Link>
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Programs;
