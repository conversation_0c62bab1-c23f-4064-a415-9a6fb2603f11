
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>es<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Book, GraduationCap, Calculator } from 'lucide-react';

interface ProgramDetailProps {
  program: {
    name: string;
    description: string;
    duration: string;
    faculty: string;
    degreeAwarded: string;
    careerOpportunities: string[];
    courseHighlights: {
      year: string;
      courses: string[];
    }[];
    admissionRequirements: string[];
    programObjectives: string[];
  };
}

const ProgramDetail: React.FC<ProgramDetailProps> = ({ program }) => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Program Header */}
          <div className="mb-12 text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">{program.name}</h1>
            <p className="text-xl text-gray-600">{program.faculty}</p>
          </div>

          {/* Program Overview */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Book className="h-6 w-6" />
                Program Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">{program.description}</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <GraduationCap className="h-5 w-5 text-gray-500" />
                  <span>Degree Awarded: {program.degreeAwarded}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calculator className="h-5 w-5 text-gray-500" />
                  <span>Duration: {program.duration}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Program Objectives */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Program Objectives</CardTitle>
              <CardDescription>What you'll achieve with this program</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="list-disc pl-6 space-y-2">
                {program.programObjectives.map((objective, index) => (
                  <li key={index} className="text-gray-700">{objective}</li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Course Structure */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Course Structure</CardTitle>
              <CardDescription>Year by year breakdown of courses</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {program.courseHighlights.map((year, index) => (
                  <div key={index}>
                    <h4 className="font-semibold mb-2">{year.year}</h4>
                    <ul className="list-disc pl-6 space-y-1">
                      {year.courses.map((course, courseIndex) => (
                        <li key={courseIndex} className="text-gray-700">{course}</li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Career Opportunities */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Career Opportunities</CardTitle>
              <CardDescription>Potential career paths after graduation</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="list-disc pl-6 space-y-2">
                {program.careerOpportunities.map((opportunity, index) => (
                  <li key={index} className="text-gray-700">{opportunity}</li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Admission Requirements */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Admission Requirements</CardTitle>
              <CardDescription>What you need to apply</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="list-disc pl-6 space-y-2">
                {program.admissionRequirements.map((requirement, index) => (
                  <li key={index} className="text-gray-700">{requirement}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProgramDetail;
