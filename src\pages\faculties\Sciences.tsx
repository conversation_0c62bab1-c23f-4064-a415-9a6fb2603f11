
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { But<PERSON> } from '@/components/ui/button';
import { Microscope, Book, Brain, Atom, ArrowRight } from 'lucide-react';

const Sciences = () => {
  const departments = [
    {
      name: "Computer Science",
      description: "Leading research in AI, machine learning, cybersecurity, and software engineering.",
      icon: <Brain className="w-8 h-8 text-purple-600" />,
      programs: ["BSc in Computer Science", "MSc in Artificial Intelligence", "PhD in Computer Science"],
      path: "/faculties/sciences/computer-science"
    },
    {
      name: "Biology",
      description: "Advancing knowledge in molecular biology, genetics, ecology, and biotechnology.",
      icon: <Microscope className="w-8 h-8 text-purple-600" />,
      programs: ["BSc in Biology", "MSc in Molecular Biology", "PhD in Life Sciences"],
      path: "/faculties/sciences/biology"
    },
    {
      name: "Mathematics",
      description: "Excellence in pure and applied mathematics, statistics, and mathematical modeling.",
      icon: <Book className="w-8 h-8 text-purple-600" />,
      programs: ["BSc in Mathematics", "MSc in Applied Mathematics", "PhD in Mathematics"],
      path: "/faculties/sciences/mathematics"
    },
    {
      name: "Chemistry",
      description: "Pioneering research in organic chemistry, materials science, and biochemistry.",
      icon: <Atom className="w-8 h-8 text-purple-600" />,
      programs: ["BSc in Chemistry", "MSc in Chemical Sciences", "PhD in Chemistry"],
      path: "/faculties/sciences/chemistry"
    }
  ];

  const researchHighlights = [
    {
      title: "Quantum Computing Research",
      description: "Development of quantum algorithms and quantum error correction methods.",
      department: "Computer Science"
    },
    {
      title: "CRISPR Gene Editing",
      description: "Advanced research in genetic engineering and therapeutic applications.",
      department: "Biology"
    },
    {
      title: "Data Science Applications",
      description: "Mathematical modeling for big data analytics and machine learning.",
      department: "Mathematics"
    },
    {
      title: "Green Chemistry Initiative",
      description: "Sustainable chemical processes and environmental protection research.",
      department: "Chemistry"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-purple-900 via-purple-800 to-purple-900 text-white py-16">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Faculty of Sciences</h1>
            <p className="text-xl md:text-2xl opacity-90 max-w-3xl">
              Advancing scientific knowledge through innovative research and exceptional education in computer science, 
              biology, mathematics, and chemistry.
            </p>
          </div>
        </div>

        {/* Departments Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12 text-purple-900">Our Departments</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {departments.map((dept, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="flex flex-row items-center gap-4">
                    {dept.icon}
                    <CardTitle className="text-xl">{dept.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{dept.description}</p>
                    <h4 className="font-semibold mb-2">Programs Offered:</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-600 mb-4">
                      {dept.programs.map((program, idx) => (
                        <li key={idx}>{program}</li>
                      ))}
                    </ul>
                    <Button asChild variant="outline" className="mt-2">
                      <Link to={dept.path} className="flex items-center">
                        Learn More <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Research Highlights Section */}
        <section className="py-16 bg-purple-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12 text-purple-900">Research Highlights</h2>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Research Area</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Department</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {researchHighlights.map((research, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{research.title}</TableCell>
                    <TableCell>{research.description}</TableCell>
                    <TableCell>{research.department}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </section>

        {/* Get in Touch Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-6 text-purple-900">Get in Touch</h2>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              Interested in joining our faculty? Contact us to learn more about our programs, 
              research opportunities, and admission requirements.
            </p>
            <Button asChild size="lg">
              <Link to="/contact">Contact Us</Link>
            </Button>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Sciences;
