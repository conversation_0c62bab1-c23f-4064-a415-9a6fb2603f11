
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Psychology = () => {
  const programData = {
    name: "Bachelor of Arts in Psychology",
    description: "Our Psychology program explores human behavior, mental processes, and emotional development. Students gain a comprehensive understanding of psychological theories and research methods, preparing them for diverse career paths.",
    duration: "3 years",
    faculty: "Faculty of Arts",
    degreeAwarded: "Bachelor of Arts (BA) in Psychology",
    careerOpportunities: [
      "Mental Health Counselor",
      "Human Resources Specialist",
      "Research Assistant",
      "Social Services Coordinator",
      "Behavior Analyst",
      "Child Development Specialist",
      "Rehabilitation Specialist",
      "Academic Counselor"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Introduction to Psychology",
          "Developmental Psychology",
          "Cognitive Psychology",
          "Statistics for Psychology",
          "Research Methods"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Social Psychology",
          "Abnormal Psychology",
          "Personality Psychology",
          "Biological Psychology",
          "Psychological Assessment"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Clinical Psychology",
          "Health Psychology",
          "Cross-Cultural Psychology",
          "Applied Psychology",
          "Senior Thesis"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Minimum GPA of 2.8",
      "Interest in human behavior",
      "Basic understanding of scientific methods",
      "English language proficiency",
      "Statement of purpose"
    ],
    programObjectives: [
      "Develop strong understanding of psychological theories and concepts",
      "Master research methods and statistical analysis in psychology",
      "Apply psychological knowledge to analyze human behavior",
      "Understand ethical considerations in psychological research and practice",
      "Develop critical thinking and problem-solving skills",
      "Prepare for careers in mental health, education, and human services"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default Psychology;
