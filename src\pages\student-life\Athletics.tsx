
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Trophy } from 'lucide-react';

const Athletics = () => {
  const sports = [
    {
      category: "Varsity Sports",
      teams: [
        "Men's Basketball",
        "Women's Basketball",
        "Men's Soccer",
        "Women's Soccer",
        "Swimming"
      ]
    },
    {
      category: "Intramural Sports",
      teams: [
        "Volleyball",
        "Tennis",
        "Table Tennis",
        "Badminton",
        "Futsal"
      ]
    },
    {
      category: "Fitness Programs",
      teams: [
        "Yoga Classes",
        "Strength Training",
        "Cardio Sessions",
        "CrossFit",
        "Dance Fitness"
      ]
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Trophy className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Athletics & Recreation</h1>
          </div>
          
          <p className="text-lg text-gray-600 mb-8">
            AUL offers a comprehensive athletics program that promotes physical fitness, 
            teamwork, and competitive excellence. Join our teams and be part of our 
            winning tradition.
          </p>

          <div className="grid md:grid-cols-3 gap-8">
            {sports.map((category, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{category.category}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {category.teams.map((team, idx) => (
                      <li key={idx} className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-aul-gold rounded-full"></span>
                        <span className="text-gray-600">{team}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Athletics;
