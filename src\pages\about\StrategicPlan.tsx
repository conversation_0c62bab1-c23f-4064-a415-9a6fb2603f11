
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const StrategicPlan = () => {
  const objectives = [
    {
      title: "Academic Excellence",
      period: "2024-2028",
      goals: [
        "Enhance research capabilities and output",
        "Expand international partnerships",
        "Develop innovative teaching methodologies",
        "Strengthen graduate programs"
      ]
    },
    {
      title: "Student Success",
      period: "2024-2028",
      goals: [
        "Improve student support services",
        "Increase scholarship opportunities",
        "Enhance career development programs",
        "Expand internship partnerships"
      ]
    },
    {
      title: "Campus Development",
      period: "2024-2028",
      goals: [
        "Modernize learning facilities",
        "Implement sustainable practices",
        "Expand digital infrastructure",
        "Create collaborative spaces"
      ]
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">Strategic Plan</h1>
          
          <div className="bg-aul-navy text-white p-8 rounded-lg mb-8">
            <h2 className="text-2xl font-bold mb-4">Vision 2028</h2>
            <p className="text-lg">
              To be recognized as a leading institution of higher education in the region, 
              known for academic excellence, innovative research, and positive impact on society.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {objectives.map((objective, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{objective.title}</CardTitle>
                  <div className="text-sm text-gray-500">{objective.period}</div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {objective.goals.map((goal, idx) => (
                      <li key={idx} className="flex items-start gap-2">
                        <span className="w-2 h-2 bg-aul-gold rounded-full mt-2"></span>
                        <span className="text-gray-600">{goal}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default StrategicPlan;
