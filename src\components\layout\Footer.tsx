
import React from 'react';
import { Link } from 'react-router-dom';
import { Facebook, Twitter, Instagram, Linkedin, Youtube, MapPin, Phone, Mail } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-aul-navy text-white pt-12 pb-6">
      <div className="aul-container">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Column 1 - About */}
          <div>
            <h3 className="text-xl font-bold mb-4">AUL University</h3>
            <p className="mb-4 text-gray-300">
              Arts, Sciences and Technology University in Lebanon (AUL) is dedicated to academic excellence, 
              innovation, and creating future leaders in various fields.
            </p>
            <div className="flex space-x-4">
              <a href="https://facebook.com" className="text-white hover:text-aul-gold transition-colors">
                <Facebook size={20} />
              </a>
              <a href="https://twitter.com" className="text-white hover:text-aul-gold transition-colors">
                <Twitter size={20} />
              </a>
              <a href="https://instagram.com" className="text-white hover:text-aul-gold transition-colors">
                <Instagram size={20} />
              </a>
              <a href="https://linkedin.com" className="text-white hover:text-aul-gold transition-colors">
                <Linkedin size={20} />
              </a>
              <a href="https://youtube.com" className="text-white hover:text-aul-gold transition-colors">
                <Youtube size={20} />
              </a>
            </div>
          </div>

          {/* Column 2 - Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/about" className="text-gray-300 hover:text-aul-gold transition-colors">
                  About AUL
                </Link>
              </li>
              <li>
                <Link to="/admissions" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Admissions
                </Link>
              </li>
              <li>
                <Link to="/academics" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Academics
                </Link>
              </li>
              <li>
                <Link to="/research" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Research
                </Link>
              </li>
              <li>
                <Link to="/student-life" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Student Life
                </Link>
              </li>
              <li>
                <Link to="/news-events" className="text-gray-300 hover:text-aul-gold transition-colors">
                  News & Events
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 3 - Important Links */}
          <div>
            <h3 className="text-xl font-bold mb-4">Important Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/privacy-policy" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link to="/terms-of-service" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link to="/emergency" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Emergency Information
                </Link>
              </li>
              <li>
                <Link to="/careers" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Careers at AUL
                </Link>
              </li>
              <li>
                <Link to="/directory" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Directory
                </Link>
              </li>
              <li>
                <Link to="/library" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Library
                </Link>
              </li>
              <li>
                <Link to="/alumni" className="text-gray-300 hover:text-aul-gold transition-colors">
                  Alumni
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 4 - Contact */}
          <div>
            <h3 className="text-xl font-bold mb-4">Contact Us</h3>
            <ul className="space-y-4">
              <li className="flex">
                <MapPin className="mr-2 h-5 w-5 text-aul-gold" />
                <span className="text-gray-300">123 University Street, Beirut, Lebanon</span>
              </li>
              <li className="flex">
                <Phone className="mr-2 h-5 w-5 text-aul-gold" />
                <span className="text-gray-300">+961 123 456 789</span>
              </li>
              <li className="flex">
                <Mail className="mr-2 h-5 w-5 text-aul-gold" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-aul-gold transition-colors">
                  <EMAIL>
                </a>
              </li>
            </ul>
            <div className="mt-6">
              <h4 className="text-lg font-semibold mb-2">Subscribe to Newsletter</h4>
              <div className="flex">
                <input 
                  type="email" 
                  placeholder="Your email" 
                  className="px-4 py-2 w-full text-gray-900 rounded-l focus:outline-none"
                />
                <button className="bg-aul-gold text-aul-navy px-4 py-2 rounded-r font-medium hover:bg-yellow-400 transition-colors">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 pt-6 border-t border-gray-700">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              &copy; {new Date().getFullYear()} Arts, Sciences and Technology University in Lebanon. All rights reserved.
            </p>
            <div className="flex space-x-4 mt-4 md:mt-0">
              <Link to="/accessibility" className="text-gray-400 text-sm hover:text-aul-gold transition-colors">
                Accessibility
              </Link>
              <Link to="/sitemap" className="text-gray-400 text-sm hover:text-aul-gold transition-colors">
                Sitemap
              </Link>
              <Link to="/feedback" className="text-gray-400 text-sm hover:text-aul-gold transition-colors">
                Feedback
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
