
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Book } from 'lucide-react';

const Faculties = () => {
  const faculties = [
    {
      name: "Faculty of Sciences",
      departments: ["Computer Science", "Biology", "Mathematics", "Chemistry"],
      description: "Leading research and innovation in fundamental and applied sciences."
    },
    {
      name: "Faculty of Engineering",
      departments: ["Civil Engineering", "Electrical Engineering", "Mechanical Engineering", "Computer Engineering"],
      description: "Advancing technological solutions for tomorrow's challenges."
    },
    {
      name: "Faculty of Business",
      departments: ["Business Administration", "Finance", "Marketing", "Accounting"],
      description: "Developing future business leaders and entrepreneurs."
    },
    {
      name: "Faculty of Arts",
      departments: ["Graphic Design", "Digital Media", "English Literature", "Psychology"],
      description: "Fostering creativity, critical thinking, and cultural understanding."
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Book className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Our Faculties</h1>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {faculties.map((faculty, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle>{faculty.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{faculty.description}</p>
                  <h3 className="font-semibold mb-2">Departments:</h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-600">
                    {faculty.departments.map((dept, idx) => (
                      <li key={idx}>{dept}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Faculties;
