
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Menu,
  X,
  ChevronDown,
  Search,
  Globe
} from 'lucide-react';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
  NavigationMenuLink,
} from "@/components/ui/navigation-menu";
import { Button } from "@/components/ui/button";
import { cn } from '@/lib/utils';

const navigationItems = [
  {
    title: "About",
    href: "/about",
    subItems: [
      { title: "Mission & Vision", href: "/about/mission-vision" },
      { title: "History", href: "/about/history" },
      { title: "Accreditations", href: "/about/accreditations" },
      { title: "Administration", href: "/about/administration" },
      { title: "Campus Locations", href: "/about/campus-locations" },
      { title: "Strategic Plan", href: "/about/strategic-plan" }
    ]
  },
  {
    title: "Admissions",
    href: "/admissions",
    subItems: [
      { title: "Pre-Application", href: "/admissions/pre-application" },
      { title: "Required Documents", href: "/admissions/required-documents" },
      { title: "Admission Fees", href: "/admissions/admission-fees" },
      { title: "Financial Aid", href: "/admissions/financial-aid" },
      { title: "Siblings Discount", href: "/admissions/siblings-discount" },
      { title: "Sports Scholarship", href: "/admissions/sports-scholarship" },
      { title: "Academic Scholarships", href: "/admissions/academic-scholarships" }
    ]
  },
  {
    title: "Academics",
    href: "/academics",
    subItems: [
      { title: "Faculties", href: "/academics/faculties" },
      { title: "Programs", href: "/academics/programs" },
      { title: "Calendar", href: "/academics/calendar" },
      { title: "Library", href: "/academics/library" }
    ]
  },
  {
    title: "Faculties",
    href: "/faculties",
    subItems: [
      { title: "Faculty of Sciences", href: "/faculties/sciences" },
      { title: "Faculty of Engineering", href: "/faculties/engineering" },
      { title: "Faculty of Business", href: "/faculties/business" },
      { title: "Faculty of Arts", href: "/faculties/arts" },
      { title: "Faculty of Law", href: "/faculties/law" }
    ]
  },
  {
    title: "Research",
    href: "/research",
    subItems: [
      { title: "Research Centers", href: "/research/centers" },
      { title: "Projects", href: "/research/projects" },
      { title: "Publications", href: "/research/publications" },
      { title: "Grants", href: "/research/grants" },
      { title: "Ethics Committee", href: "/research/ethics" },
      { title: "Partnerships", href: "/research/partnerships" }
    ]
  },
  {
    title: "Student Life",
    href: "/student-life",
    subItems: [
      { title: "Clubs & Societies", href: "/student-life/clubs" },
      { title: "Housing", href: "/student-life/housing" },
      { title: "Dining", href: "/student-life/dining" },
      { title: "Athletics", href: "/student-life/athletics" },
      { title: "Counseling Services", href: "/student-life/counseling" },
      { title: "Campus Facilities", href: "/student-life/facilities" },
      { title: "Student Success Stories", href: "/student-life/success-stories" }
    ]
  },
  {
    title: "News & Events",
    href: "/news-events",
    subItems: [
      { title: "News", href: "/news-events/news" },
      { title: "Events", href: "/news-events/events" },
      { title: "Academic Calendar", href: "/news-events/academic-calendar" },
      { title: "Archived Newsletters", href: "/news-events/newsletters" }
    ]
  }
];

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeLanguage, setActiveLanguage] = useState("English");

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const changeLanguage = (language: string) => {
    setActiveLanguage(language);
    // In a real implementation, this would change the site language
  };

  return (
    <header className="bg-white shadow-md">
      {/* Top bar with secondary navigation */}
      <div className="bg-aul-navy text-white py-2">
        <div className="aul-container flex justify-between items-center">
          <div className="flex items-center space-x-4 text-sm">
            <Link to="/for-parents" className="hover:text-aul-gold transition-colors">For Parents</Link>
            <Link to="/for-faculty" className="hover:text-aul-gold transition-colors">For Faculty</Link>
            <Link to="/for-staff" className="hover:text-aul-gold transition-colors">For Staff</Link>
            <Link to="/alumni" className="hover:text-aul-gold transition-colors">Alumni</Link>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1 cursor-pointer group">
              <Globe size={16} />
              <div className="relative">
                <span className="mr-1">{activeLanguage}</span>
                <ChevronDown size={14} />
                <div className="absolute right-0 mt-2 bg-white text-aul-navy rounded-md shadow-lg py-1 z-50 w-28 hidden group-hover:block">
                  <button
                    onClick={() => changeLanguage("English")}
                    className="block px-4 py-1 hover:bg-gray-100 w-full text-left"
                  >
                    English
                  </button>
                  <button
                    onClick={() => changeLanguage("العربية")}
                    className="block px-4 py-1 hover:bg-gray-100 w-full text-left"
                  >
                    العربية
                  </button>
                  <button
                    onClick={() => changeLanguage("Français")}
                    className="block px-4 py-1 hover:bg-gray-100 w-full text-left"
                  >
                    Français
                  </button>
                </div>
              </div>
            </div>
            <Link to="/search" className="hover:text-aul-gold transition-colors">
              <Search size={16} />
            </Link>
          </div>
        </div>
      </div>

      {/* Main navigation */}
      <div className="aul-container py-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <div className="bg-aul-navy text-white font-bold py-2 px-4 rounded-md text-2xl mr-3 flex items-center justify-center">
              AUL
            </div>
            <div>
              <span className="font-montserrat font-bold text-2xl tracking-tight text-aul-navy">AUL</span>
              <span className="hidden md:inline-block ml-2 text-sm text-gray-600">Arts, Sciences and Technology University in Lebanon</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2">
            <div className="flex items-center mr-6">
              <Link to="/" className="flex items-center">
                <div className="bg-aul-navy text-white font-bold py-2 px-3 rounded-md flex items-center">
                  <span className="text-lg">AUL</span>
                  <div className="ml-1 h-4 w-1 bg-aul-gold rounded-full"></div>
                </div>
              </Link>
              <div className="h-8 w-px bg-gray-300 mx-4"></div>
            </div>
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <Link to="/">
                    <NavigationMenuLink className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors hover:bg-aul-navy hover:text-white focus:bg-aul-navy focus:text-white focus:outline-none disabled:pointer-events-none disabled:opacity-50"
                    )}>
                      Home
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>

                {navigationItems.map((item, index) => (
                  <NavigationMenuItem key={index}>
                    <NavigationMenuTrigger className="text-sm font-medium">
                      {item.title}
                    </NavigationMenuTrigger>
                    <NavigationMenuContent className="bg-white border shadow-lg rounded-md overflow-hidden">
                      <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                        {item.subItems.map((subItem, subIndex) => (
                          <li key={subIndex}>
                            <NavigationMenuLink asChild>
                              <Link
                                to={subItem.href}
                                className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                              >
                                <div className="text-sm font-medium leading-none">{subItem.title}</div>
                              </Link>
                            </NavigationMenuLink>
                          </li>
                        ))}
                      </ul>
                    </NavigationMenuContent>
                  </NavigationMenuItem>
                ))}

                <NavigationMenuItem>
                  <Link to="/contact">
                    <NavigationMenuLink className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors hover:bg-aul-navy hover:text-white focus:bg-aul-navy focus:text-white focus:outline-none disabled:pointer-events-none disabled:opacity-50"
                    )}>
                      Contact Us
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            <div className="flex space-x-2">
              <Button variant="default" size="sm" className="bg-aul-red hover:bg-red-700">
                Apply Now
              </Button>
              <Button variant="outline" size="sm">
                Virtual Tour
              </Button>
            </div>
          </div>

          {/* Mobile menu button */}
          <button
            onClick={toggleMenu}
            className="lg:hidden text-aul-navy focus:outline-none"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white fixed inset-0 z-50 overflow-y-auto pt-16">
          <div className="apy-container py-4">
            <div className="flex items-center mb-4 border-b border-gray-200 pb-4">
              <div className="bg-aul-navy text-white font-bold py-2 px-3 rounded-md mr-3">
                AUL
              </div>
              <span className="font-montserrat font-bold text-xl tracking-tight text-aul-navy">Arts, Sciences and Technology University</span>
            </div>
            <div className="space-y-1">
              <Link
                to="/"
                className="block py-2 text-aul-navy font-medium border-b border-gray-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Home
              </Link>

              {navigationItems.map((item, index) => (
                <div key={index} className="py-2 border-b border-gray-200">
                  <Link
                    to={item.href}
                    className="block text-aul-navy font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.title}
                  </Link>
                  <div className="mt-1 ml-4 space-y-1">
                    {item.subItems.map((subItem, subIndex) => (
                      <Link
                        key={subIndex}
                        to={subItem.href}
                        className="block py-1 text-sm text-gray-700 hover:text-aul-navy"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {subItem.title}
                      </Link>
                    ))}
                  </div>
                </div>
              ))}

              <Link
                to="/contact"
                className="block py-2 text-aul-navy font-medium border-b border-gray-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Contact Us
              </Link>

              <div className="pt-4 space-y-2">
                <Button variant="default" className="w-full bg-aul-red hover:bg-red-700">
                  Apply Now
                </Button>
                <Button variant="outline" className="w-full">
                  Virtual Tour
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
