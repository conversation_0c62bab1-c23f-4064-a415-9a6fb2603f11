
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const ElectricalEngineering = () => {
  const programData = {
    name: "Bachelor of Engineering in Electrical Engineering",
    description: "Our Electrical Engineering program covers the study of electricity, electronics, and electromagnetism. Students learn to design, develop, and test electrical systems and devices for a variety of applications.",
    duration: "4 years",
    faculty: "Faculty of Engineering",
    degreeAwarded: "Bachelor of Engineering (BEng) in Electrical Engineering",
    careerOpportunities: [
      "Electrical Engineer",
      "Power Systems Engineer",
      "Controls Engineer",
      "Electronics Engineer",
      "Communication Systems Engineer",
      "Renewable Energy Engineer",
      "Robotics Engineer",
      "Semiconductor Engineer"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Electric Circuits",
          "Digital Logic Design",
          "Engineering Mathematics",
          "Physics for Engineers",
          "Introduction to Programming"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Analog Electronics",
          "Electromagnetic Field Theory",
          "Signals and Systems",
          "Microprocessors",
          "Electrical Measurements"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Power Systems",
          "Control Systems",
          "Digital Signal Processing",
          "Communication Systems",
          "Electronic Devices"
        ]
      },
      {
        year: "Fourth Year",
        courses: [
          "Power Electronics",
          "VLSI Design",
          "Renewable Energy Systems",
          "Capstone Design Project",
          "Professional Ethics"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Minimum GPA of 3.0",
      "Strong background in Mathematics and Physics",
      "Problem-solving aptitude",
      "English language proficiency",
      "Statement of purpose"
    ],
    programObjectives: [
      "Develop strong understanding of electrical engineering principles",
      "Master design and analysis of electrical systems",
      "Apply engineering knowledge to solve complex electrical problems",
      "Use modern engineering tools and technologies effectively",
      "Understand professional and ethical responsibilities of engineers",
      "Prepare for careers in power, electronics, and communications"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default ElectricalEngineering;
