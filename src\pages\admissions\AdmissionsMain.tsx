import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

const AdmissionsMain = () => {
  const requiredDocuments = [
    "Admission Application Form (4 pages)",
    "Three recent passport-size colored photos (size 3 x 4 cm)",
    "Certified copy of Identity Card or Passport",
    "Certified copy of Family Register",
    "Certified copy of Official Lebanese BAC II Certificate or its equivalent, or freshman authorization along with SAT",
    "Certified copy of High School grades of the last three years",
    "Certified copies of degree(s) and official transcript(s) from previous university stamped by the Lebanese Ministry of Education & Higher Education, in addition to their equivalence (for graduate programs)",
    "International Testing scores and certificates (if available)",
    "Medical Report",
    "NSSF letter (If available)",
    "Proof of Residency letter"
  ];

  const admissionFees = [
    "Application fees",
    "Language Entrance Exam fees for all majors (if required)",
    "Faculty of Sciences & Fine Arts: Math Entrance Exam for majors in science",
    "Faculty of Engineering: Math & Physics Entrance Exams",
    "Faculty of Business: Business Math Entrance Exam",
    "NSFF fees (if required)"
  ];

  const financialAidTypes = [
    { title: "Siblings", description: "Discount for students with siblings enrolled at AUL" },
    { title: "Sports", description: "Scholarships for students with exceptional athletic abilities" },
    { title: "Academic Scholarships", description: "Merit-based scholarships for outstanding academic achievement" },
    { title: "Financial Aid", description: "Need-based financial assistance for qualified students" }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-aul-navy text-white py-16">
          <div className="aul-container">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">UNLOCK YOUR JOURNEY</h1>
            <p className="text-xl mb-8">ADMISSIONS OFFICE</p>
            <div className="flex flex-col md:flex-row gap-6">
              <div className="bg-white text-aul-navy p-8 rounded-lg text-center flex-1">
                <h2 className="text-2xl font-bold mb-4">DID YOU CHOOSE YOUR MAJOR?</h2>
                <div className="mt-6">
                  <h3 className="text-xl font-bold text-aul-gold mb-4">YES!</h3>
                  <Button className="w-full bg-aul-gold text-aul-navy hover:bg-yellow-400">
                    <Link to="/admissions/pre-application">Fill my PRE-APPLICATION</Link>
                  </Button>
                </div>
                <div className="mt-8">
                  <h3 className="text-xl font-bold mb-4">Not Yet...</h3>
                  <Button variant="outline" className="w-full border-aul-navy">
                    <Link to="/academics">Check Out Majors</Link>
                  </Button>
                </div>
              </div>
              <div className="bg-white text-aul-navy p-8 rounded-lg flex-1">
                <h2 className="text-2xl font-bold mb-4">After Filling Your Pre-application</h2>
                <p className="text-gray-600 mb-6">
                  AUL applicants shall fill the Admission Application Form found at the Campus of registration and shall provide the respective Office of Admissions with all the documents required according to assigned deadlines.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Required Documents Section */}
        <section className="py-12 bg-gray-50">
          <div className="aul-container">
            <Tabs defaultValue="documents" className="w-full">
              <TabsList className="grid grid-cols-3 mb-8">
                <TabsTrigger value="documents">Required Documents</TabsTrigger>
                <TabsTrigger value="fees">Admission Fees</TabsTrigger>
                <TabsTrigger value="aid">Financial Aid</TabsTrigger>
              </TabsList>
              
              <TabsContent value="documents" className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-2xl font-bold text-aul-navy mb-6">Required Documents</h2>
                <ul className="space-y-4">
                  {requiredDocuments.map((doc, index) => (
                    <li key={index} className="flex items-start">
                      <div className="bg-aul-navy text-white font-bold rounded-full w-8 h-8 flex items-center justify-center mr-3 flex-shrink-0">
                        E
                      </div>
                      <span className="text-gray-700">{doc}</span>
                    </li>
                  ))}
                </ul>
              </TabsContent>
              
              <TabsContent value="fees" className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-2xl font-bold text-aul-navy mb-6">Admission Fees</h2>
                <ul className="space-y-4">
                  {admissionFees.map((fee, index) => (
                    <li key={index} className="flex items-start">
                      <div className="bg-aul-navy text-white font-bold rounded-full w-8 h-8 flex items-center justify-center mr-3 flex-shrink-0">
                        E
                      </div>
                      <span className="text-gray-700">{fee}</span>
                    </li>
                  ))}
                </ul>
              </TabsContent>
              
              <TabsContent value="aid" className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-2xl font-bold text-aul-navy mb-6">FINANCIAL AID</h2>
                <p className="text-gray-600 mb-6">
                  To be qualified for financial aid from AUL, an applicant must be a degree-seeking student at the University. Applications are available at the Student Affairs department and should be completed before deadline set by the university's calendar. Once granted, financial aid will be activated promptly.
                </p>
                
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
                  {financialAidTypes.map((type, index) => (
                    <Card key={index} className="border-t-4 border-aul-navy">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">{type.title}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-600">{type.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* Pre-Application Form Section */}
        <section className="py-12">
          <div className="aul-container">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <img 
                  src="https://i0.wp.com/aul.edu.lb/wp-content/uploads/2021/05/130A4051-copy-3.jpg?resize=917%2C1135&ssl=1" 
                  alt="AUL Campus" 
                  className="rounded-lg shadow-lg"
                />
              </div>
              <div>
                <h2 className="text-3xl font-bold text-aul-navy mb-6">PRE-APPLICATION FORM</h2>
                <p className="text-gray-600 mb-6">
                  Start your journey at AUL by filling out our pre-application form. Our admissions team will guide you through the next steps of the application process.
                </p>
                <Button className="w-full bg-aul-gold text-aul-navy hover:bg-yellow-400 py-6 text-lg">
                  <Link to="/admissions/pre-application">Fill Pre-Application Form</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-12 bg-gray-50">
          <div className="aul-container">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-aul-navy mb-4">NEED ASSISTANCE?</h2>
              <p className="text-xl text-gray-600">Our admissions team is here to help you with any questions you may have.</p>
            </div>
            <div className="flex justify-center">
              <Button className="bg-aul-navy hover:bg-blue-900 text-lg py-6 px-8">
                <Link to="/contact">Contact Admissions Office</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default AdmissionsMain;
