
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const Housing = () => {
  const residenceHalls = [
    {
      name: "Cedar Hall",
      type: "First-Year Students",
      features: [
        "Double occupancy rooms",
        "Community lounges",
        "Study rooms",
        "24/7 security"
      ],
      amenities: "Shared bathrooms, laundry facilities, Wi-Fi"
    },
    {
      name: "Pine Apartments",
      type: "Upper-class Students",
      features: [
        "Single and double rooms",
        "Kitchen facilities",
        "Common areas",
        "Quiet study spaces"
      ],
      amenities: "Private bathrooms, kitchen, Wi-Fi, parking"
    },
    {
      name: "Graduate Residences",
      type: "Graduate Students",
      features: [
        "Studio apartments",
        "One-bedroom units",
        "Research spaces",
        "Professional environment"
      ],
      amenities: "Full kitchen, private bath, Wi-Fi, parking"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">Student Housing</h1>
          
          <p className="text-lg text-gray-600 mb-8">
            Experience comfortable and convenient on-campus living at AUL. Our residence halls 
            provide safe, supportive, and engaging environments for students.
          </p>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            {residenceHalls.map((hall, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{hall.name}</CardTitle>
                  <div className="text-aul-gold font-medium">{hall.type}</div>
                </CardHeader>
                <CardContent>
                  <h3 className="font-semibold mb-2">Features:</h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-600 mb-4">
                    {hall.features.map((feature, idx) => (
                      <li key={idx}>{feature}</li>
                    ))}
                  </ul>
                  <div className="text-gray-600">
                    <span className="font-semibold">Amenities: </span>
                    {hall.amenities}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <Button size="lg">Apply for Housing</Button>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Housing;
