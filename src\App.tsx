import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import ComingSoonPage from "./components/common/ComingSoonPage";

// Page imports
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Academics from "./pages/Academics";
import MissionVision from "./pages/about/MissionVision";
import History from "./pages/about/History";
import Accreditations from "./pages/about/Accreditations";
import Administration from "./pages/about/Administration";
import CampusLocations from "./pages/about/CampusLocations";
import StrategicPlan from "./pages/about/StrategicPlan";
import Undergraduate from "./pages/admissions/Undergraduate";
import AdmissionsMain from "./pages/admissions/AdmissionsMain";
import StudentClubs from "./pages/student-life/StudentClubs";
import Housing from "./pages/student-life/Housing";
import Dining from "./pages/student-life/Dining";
import Athletics from "./pages/student-life/Athletics";
import Counseling from "./pages/student-life/Counseling";
import Facilities from "./pages/student-life/Facilities";

// Program imports
import ComputerScience from "./pages/programs/ComputerScience";
import BusinessAdmin from "./pages/programs/BusinessAdmin";
import Biology from "./pages/programs/Biology";
import Mathematics from "./pages/programs/Mathematics";
import Chemistry from "./pages/programs/Chemistry";
import CivilEngineering from "./pages/programs/CivilEngineering";
import ElectricalEngineering from "./pages/programs/ElectricalEngineering";
import MechanicalEngineering from "./pages/programs/MechanicalEngineering";
import ComputerEngineering from "./pages/programs/ComputerEngineering";
import Finance from "./pages/programs/Finance";
import Marketing from "./pages/programs/Marketing";
import Accounting from "./pages/programs/Accounting";
import GraphicDesign from "./pages/programs/GraphicDesign";
import DigitalMedia from "./pages/programs/DigitalMedia";
import EnglishLiterature from "./pages/programs/EnglishLiterature";
import Psychology from "./pages/programs/Psychology";
import Sciences from "./pages/faculties/Sciences";

// Create a new client with default options
const queryClient = new QueryClient();

const App = () => {
  // Add console log to help with debugging
  console.log('Rendering App component');

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/academics" element={<Academics />} />

            {/* About Routes */}
            <Route path="/about/mission-vision" element={<MissionVision />} />
            <Route path="/about/history" element={<History />} />
            <Route path="/about/accreditations" element={<Accreditations />} />
            <Route path="/about/administration" element={<Administration />} />
            <Route path="/about/campus-locations" element={<CampusLocations />} />
            <Route path="/about/strategic-plan" element={<StrategicPlan />} />

            {/* Admissions Routes */}
            <Route path="/admissions" element={<AdmissionsMain />} />
            <Route path="/admissions/undergraduate" element={<Undergraduate />} />
            <Route path="/admissions/graduate" element={
              <ComingSoonPage
                title="Graduate Admissions"
                description="Learn about our graduate programs, requirements, and application procedures. This section is being enhanced to better serve prospective graduate students."
                estimatedDate="Fall 2024"
              />
            } />
            <Route path="/admissions/international" element={
              <ComingSoonPage
                title="International Admissions"
                description="Information for international students looking to join our diverse campus community. We're preparing comprehensive guidance for your journey."
                estimatedDate="Summer 2024"
              />
            } />
            <Route path="/admissions/financial-aid" element={
              <ComingSoonPage
                title="Financial Aid"
                description="Discover the financial aid options available to help fund your education. We're updating our resources to help you understand all available opportunities."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/admissions/siblings-discount" element={
              <ComingSoonPage
                title="Siblings Discount"
                description="Learn about our special discount program for students with siblings enrolled at AUL."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/admissions/sports-scholarship" element={
              <ComingSoonPage
                title="Sports Scholarship"
                description="Information about scholarships available for students with exceptional athletic abilities."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/admissions/academic-scholarships" element={
              <ComingSoonPage
                title="Academic Scholarships"
                description="Explore our merit-based scholarships designed to support academic excellence."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/admissions/required-documents" element={
              <ComingSoonPage
                title="Required Documents"
                description="Review the list of documents needed for your application to AUL."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/admissions/admission-fees" element={
              <ComingSoonPage
                title="Admission Fees"
                description="Information about application fees and other costs associated with the admission process."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/admissions/pre-application" element={
              <ComingSoonPage
                title="Pre-Application"
                description="Start your journey at AUL by filling out our pre-application form."
                estimatedDate="Spring 2024"
              />
            } />

            {/* Student Life Routes */}
            <Route path="/student-life" element={
              <ComingSoonPage
                title="Student Life"
                description="Discover the vibrant student experience at our university. We're building a comprehensive guide to campus life."
                estimatedDate="Summer 2024"
              />
            } />
            <Route path="/student-life/clubs" element={<StudentClubs />} />
            <Route path="/student-life/housing" element={<Housing />} />
            <Route path="/student-life/dining" element={<Dining />} />
            <Route path="/student-life/athletics" element={<Athletics />} />
            <Route path="/student-life/counseling" element={<Counseling />} />
            <Route path="/student-life/facilities" element={<Facilities />} />
            <Route path="/student-life/success-stories" element={
              <ComingSoonPage
                title="Student Success Stories"
                description="Read inspiring stories of our students' achievements and experiences. We're collecting and curating these stories to share with you soon."
                estimatedDate="Summer 2024"
              />
            } />

            {/* Program Routes */}
            <Route path="/programs/computer-science" element={<ComputerScience />} />
            <Route path="/programs/business-administration" element={<BusinessAdmin />} />
            <Route path="/programs/biology" element={<Biology />} />
            <Route path="/programs/mathematics" element={<Mathematics />} />
            <Route path="/programs/chemistry" element={<Chemistry />} />
            <Route path="/programs/civil-engineering" element={<CivilEngineering />} />
            <Route path="/programs/electrical-engineering" element={<ElectricalEngineering />} />
            <Route path="/programs/mechanical-engineering" element={<MechanicalEngineering />} />
            <Route path="/programs/computer-engineering" element={<ComputerEngineering />} />
            <Route path="/programs/finance" element={<Finance />} />
            <Route path="/programs/marketing" element={<Marketing />} />
            <Route path="/programs/accounting" element={<Accounting />} />
            <Route path="/programs/graphic-design" element={<GraphicDesign />} />
            <Route path="/programs/digital-media" element={<DigitalMedia />} />
            <Route path="/programs/english-literature" element={<EnglishLiterature />} />
            <Route path="/programs/psychology" element={<Psychology />} />

            <Route path="/faculties/sciences/computer-science" element={<ComputerScience />} />
            <Route path="/faculties/sciences/biology" element={<Biology />} />
            <Route path="/faculties/sciences/mathematics" element={<Mathematics />} />
            <Route path="/faculties/sciences/chemistry" element={<Chemistry />} />

            <Route path="/faculties/engineering/civil-engineering" element={<CivilEngineering />} />
            <Route path="/faculties/engineering/electrical-engineering" element={<ElectricalEngineering />} />
            <Route path="/faculties/engineering/mechanical-engineering" element={<MechanicalEngineering />} />
            <Route path="/faculties/engineering/computer-engineering" element={<ComputerEngineering />} />

            <Route path="/faculties/business/business-administration" element={<BusinessAdmin />} />
            <Route path="/faculties/business/finance" element={<Finance />} />
            <Route path="/faculties/business/marketing" element={<Marketing />} />
            <Route path="/faculties/business/accounting" element={<Accounting />} />

            <Route path="/faculties/arts/graphic-design" element={<GraphicDesign />} />
            <Route path="/faculties/arts/digital-media" element={<DigitalMedia />} />
            <Route path="/faculties/arts/english-literature" element={<EnglishLiterature />} />
            <Route path="/faculties/arts/psychology" element={<Psychology />} />

            {/* Other Routes */}
            <Route path="/about" element={
              <ComingSoonPage
                title="About Our University"
                description="Learn about our history, mission, and vision. We're building a comprehensive overview of our institution."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/faculties" element={
              <ComingSoonPage
                title="Our Faculties"
                description="Explore our academic faculties and their diverse programs. We're creating detailed profiles of each faculty."
                estimatedDate="Summer 2024"
              />
            } />
            <Route path="/faculties/sciences" element={<Sciences />} />
            <Route path="/faculties/engineering" element={
              <ComingSoonPage
                title="Faculty of Engineering"
                description="Learn about our engineering programs and research initiatives. We're building detailed content about this faculty."
                estimatedDate="Summer 2024"
              />
            } />
            <Route path="/faculties/business" element={
              <ComingSoonPage
                title="Faculty of Business"
                description="Explore our business and management programs. We're creating comprehensive information about this faculty."
                estimatedDate="Summer 2024"
              />
            } />
            <Route path="/faculties/arts" element={
              <ComingSoonPage
                title="Faculty of Arts"
                description="Discover our programs in humanities, social sciences, and creative arts. We're developing detailed content about this faculty."
                estimatedDate="Summer 2024"
              />
            } />
            <Route path="/faculties/law" element={
              <ComingSoonPage
                title="Faculty of Law"
                description="Learn about our legal education programs and opportunities. We're building comprehensive information about this faculty."
                estimatedDate="Fall 2024"
              />
            } />

            <Route path="/research" element={
              <ComingSoonPage
                title="Research at AUL"
                description="Discover our groundbreaking research initiatives and contributions to various fields of study."
                estimatedDate="Fall 2024"
              />
            } />
            <Route path="/research/centers" element={
              <ComingSoonPage
                title="Research Centers"
                description="Learn about our specialized research centers and their innovative work across different disciplines."
                estimatedDate="Fall 2024"
              />
            } />
            <Route path="/research/projects" element={
              <ComingSoonPage
                title="Research Projects"
                description="Explore our ongoing and completed research projects across various disciplines."
                estimatedDate="Winter 2024"
              />
            } />
            <Route path="/research/publications" element={
              <ComingSoonPage
                title="Research Publications"
                description="Access our faculty and student research publications and scholarly works."
                estimatedDate="Winter 2024"
              />
            } />
            <Route path="/research/grants" element={
              <ComingSoonPage
                title="Research Grants"
                description="Information about available research grants and funding opportunities."
                estimatedDate="Fall 2024"
              />
            } />
            <Route path="/research/ethics" element={
              <ComingSoonPage
                title="Research Ethics"
                description="Learn about our commitment to ethical research practices and guidelines."
                estimatedDate="Fall 2024"
              />
            } />
            <Route path="/research/partnerships" element={
              <ComingSoonPage
                title="Research Partnerships"
                description="Explore our collaborative research initiatives with industry and academic partners."
                estimatedDate="Winter 2024"
              />
            } />

            <Route path="/news-events" element={
              <ComingSoonPage
                title="News & Events"
                description="Stay updated with the latest campus news, upcoming events, and important announcements."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/news-events/news" element={
              <ComingSoonPage
                title="University News"
                description="Read the latest news and updates from our university community."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/news-events/events" element={
              <ComingSoonPage
                title="Campus Events"
                description="Discover upcoming events, lectures, and activities happening on campus."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/news-events/academic-calendar" element={
              <ComingSoonPage
                title="Academic Calendar"
                description="View important academic dates, deadlines, and university events for the upcoming terms."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/news-events/newsletters" element={
              <ComingSoonPage
                title="Newsletters"
                description="Subscribe to our newsletters and stay informed about university updates and achievements."
                estimatedDate="Summer 2024"
              />
            } />

            <Route path="/contact" element={
              <ComingSoonPage
                title="Contact Us"
                description="Get in touch with our departments and offices. We're updating our contact information to serve you better."
                estimatedDate="Spring 2024"
              />
            } />
            <Route path="/virtual-tour" element={
              <ComingSoonPage
                title="Virtual Campus Tour"
                description="Experience our campus virtually through an interactive 3D tour. Currently under development."
                estimatedDate="Fall 2024"
              />
            } />
            <Route path="/campus-map" element={
              <ComingSoonPage
                title="Campus Map"
                description="Navigate our campus easily with our interactive map. We're building a detailed guide to help you find your way."
                estimatedDate="Summer 2024"
              />
            } />
            <Route path="/for-parents" element={
              <ComingSoonPage
                title="For Parents"
                description="Resources and information specifically for parents of current and prospective students."
                estimatedDate="Fall 2024"
              />
            } />
            <Route path="/for-faculty" element={
              <ComingSoonPage
                title="Faculty Resources"
                description="Tools and resources to support our faculty in their teaching and research activities."
                estimatedDate="Fall 2024"
              />
            } />
            <Route path="/for-staff" element={
              <ComingSoonPage
                title="Staff Resources"
                description="Information and resources for university staff members."
                estimatedDate="Fall 2024"
              />
            } />
            <Route path="/alumni" element={
              <ComingSoonPage
                title="Alumni Network"
                description="Connect with fellow alumni and stay involved with your alma mater. New features coming soon."
                estimatedDate="Fall 2024"
              />
            } />
            <Route path="/search" element={
              <ComingSoonPage
                title="Site Search"
                description="Our advanced search feature is being developed to help you find information quickly and easily."
                estimatedDate="Summer 2024"
              />
            } />

            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
