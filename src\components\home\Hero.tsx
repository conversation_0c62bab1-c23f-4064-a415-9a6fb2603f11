
import React from 'react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const Hero = () => {
  return (
    <section className="relative h-[80vh] min-h-[500px] flex items-center overflow-hidden">
      {/* Hero background image */}
      <div className="absolute inset-0 z-0">
        <img 
          src="https://images.unsplash.com/photo-1527576539890-dfa815648363?auto=format&fit=crop&q=80" 
          alt="AUL Campus" 
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-aul-navy opacity-50"></div>
      </div>
      
      {/* Hero content */}
      <div className="aul-container relative z-10 text-white">
        <div className="max-w-3xl">
          <h1 className="text-5xl md:text-6xl font-bold mb-4 animate-fade-in">
            Welcome to AUL University
          </h1>
          <p className="text-xl md:text-2xl mb-8 animate-fade-in" style={{animationDelay: '0.2s'}}>
            Arts, Sciences and Technology University in Lebanon: 
            Shaping Tomorrow's Leaders Through Excellence in Education
          </p>
          <div className="flex flex-wrap gap-4 animate-fade-in" style={{animationDelay: '0.4s'}}>
            <Button className="bg-aul-gold hover:bg-yellow-400 text-aul-navy text-lg py-6 px-8" asChild>
              <Link to="/admissions/apply">Apply Now</Link>
            </Button>
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-aul-navy text-lg py-6 px-8" asChild>
              <Link to="/virtual-tour">Virtual Tour</Link>
            </Button>
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-aul-navy text-lg py-6 px-8" asChild>
              <Link to="/contact/request-info">Request Info</Link>
            </Button>
          </div>
        </div>
      </div>
      
      {/* Scrolling indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg 
          className="w-8 h-8 text-white" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M19 14l-7 7m0 0l-7-7m7 7V3" 
          />
        </svg>
      </div>
    </section>
  );
};

export default Hero;
