
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent } from '@/components/ui/card';

const MissionVision = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">Mission & Vision</h1>
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardContent className="pt-6">
                <h2 className="text-2xl font-bold text-aul-navy mb-4">Our Mission</h2>
                <p className="text-gray-700 mb-4">
                  AUL University is dedicated to providing excellence in education, fostering 
                  intellectual growth, and developing future leaders through innovative teaching, 
                  research, and community engagement.
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-600">
                  <li>Deliver high-quality education across diverse disciplines</li>
                  <li>Foster critical thinking and creativity</li>
                  <li>Promote research and innovation</li>
                  <li>Serve our community through engagement and outreach</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <h2 className="text-2xl font-bold text-aul-navy mb-4">Our Vision</h2>
                <p className="text-gray-700 mb-4">
                  To be a leading institution of higher education in Lebanon and the region, 
                  recognized for academic excellence, innovative research, and positive impact 
                  on society.
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-600">
                  <li>Become a center of academic excellence</li>
                  <li>Lead in research and innovation</li>
                  <li>Foster global partnerships</li>
                  <li>Drive positive societal change</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default MissionVision;
