
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { FileText } from 'lucide-react';

const Accreditations = () => {
  const accreditations = [
    {
      organization: "Higher Education Council",
      status: "Full Institutional Accreditation",
      year: "2024",
      description: "Recognized for excellence in academic standards and institutional quality."
    },
    {
      organization: "Engineering Accreditation Board",
      status: "Program-Specific Accreditation",
      year: "2023",
      description: "All engineering programs meet international standards for professional engineering education."
    },
    {
      organization: "Business Education Alliance",
      status: "Premium Accreditation",
      year: "2023",
      description: "Distinguished recognition for business and management programs."
    },
    {
      organization: "International Quality Assurance Agency",
      status: "Quality Excellence Certificate",
      year: "2024",
      description: "Meets global standards for educational quality and student outcomes."
    }
  ];

  const policyDocuments = [
    { title: "Academic Petition", filename: "Academic Petition Acad.P07.doc", category: "Academic" },
    { title: "Academic Recruitment", filename: "Academic recruitment Acad. P02.doc", category: "Academic" },
    { title: "Auditing and Archiving", filename: "Auditing and Archiving OTA-P06 .doc", category: "Administrative" },
    { title: "Change of Grade", filename: "Change of Grade R-P04.doc", category: "Registration" },
    { title: "Cheating Policy", filename: "Cheating  OTA-P02 .doc", category: "Examination" },
    { title: "Common Exam", filename: "Common Exam OTA-P07.doc", category: "Examination" },
    { title: "Course Advising", filename: "Course Advising Acad. P05.doc", category: "Academic" },
    { title: "Course File Manual", filename: "Course File Manual.docx", category: "Academic" },
    { title: "Course Offering", filename: "Course offering Acad. P06.doc", category: "Academic" },
    { title: "Course Substitution", filename: "Course substitution Acad. P04.doc", category: "Academic" },
    { title: "Graduation", filename: "Graduation  Acad. P03 .doc", category: "Academic" },
    { title: "Instructor's Error", filename: "Instructor's Error R-P03.doc", category: "Registration" },
    { title: "Late Students for Exams", filename: "Late Students for Exams OTA-P05.doc", category: "Examination" },
    { title: "Makeup Exam Manual", filename: "Makeup exam manual OTA-M02.doc", category: "Examination" },
    { title: "Makeup Exams", filename: "Makeup Exams OTA-P03.doc", category: "Examination" },
    { title: "Management of Exams", filename: "Management of Exams OTA-P04.doc", category: "Examination" },
    { title: "Program Assessment", filename: "Program assessment.doc", category: "Academic" },
    { title: "Recheck Grade", filename: "Re check grade R-P01.doc", category: "Registration" },
    { title: "Scheduling of Exams", filename: "Scheduling of Exams OTA-P01.doc", category: "Examination" },
    { title: "Teaching Methodology", filename: "Teaching Methodology Acad. P08.doc", category: "Academic" },
    { title: "Transfer Credits", filename: "Transfer Credits Acad.  P01.doc", category: "Academic" }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">Accreditations</h1>

          <Tabs defaultValue="accreditations" className="w-full mb-12">
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="accreditations">Accreditations</TabsTrigger>
              <TabsTrigger value="policies">University Policies and Procedures</TabsTrigger>
            </TabsList>

            <TabsContent value="accreditations">
              <p className="text-lg text-gray-600 mb-8">
                AUL University maintains the highest standards of educational excellence, validated through
                accreditation by leading national and international organizations.
              </p>

              <div className="grid md:grid-cols-2 gap-8">
                {accreditations.map((item, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle>{item.organization}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <span className="font-semibold">Status: </span>
                          <span className="text-green-600">{item.status}</span>
                        </div>
                        <div>
                          <span className="font-semibold">Year: </span>
                          <span>{item.year}</span>
                        </div>
                        <p className="text-gray-600">{item.description}</p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="policies">
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-aul-navy mb-4">University Policies and Procedures</h2>
                <p className="text-lg text-gray-600 mb-6">
                  AUL University is committed to maintaining transparency and clarity in all its operations.
                  Below you'll find our comprehensive set of policies and procedures that guide our academic
                  and administrative functions.
                </p>

                <Tabs defaultValue="all" className="w-full">
                  <TabsList className="flex flex-wrap mb-6">
                    <TabsTrigger value="all">All Documents</TabsTrigger>
                    <TabsTrigger value="academic">Academic</TabsTrigger>
                    <TabsTrigger value="examination">Examination</TabsTrigger>
                    <TabsTrigger value="registration">Registration</TabsTrigger>
                    <TabsTrigger value="administrative">Administrative</TabsTrigger>
                  </TabsList>

                  <TabsContent value="all">
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {policyDocuments.map((doc, index) => (
                        <Card key={index} className="overflow-hidden">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base">{doc.title}</CardTitle>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <p className="text-sm text-gray-500 mb-3">Category: {doc.category}</p>
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full flex items-center justify-center"
                              asChild
                            >
                              <a href={`/policies-and-procedures/${doc.filename}`} target="_blank" rel="noopener noreferrer">
                                <FileText className="mr-2 h-4 w-4" />
                                Download
                              </a>
                            </Button>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="academic">
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {policyDocuments
                        .filter(doc => doc.category === "Academic")
                        .map((doc, index) => (
                          <Card key={index} className="overflow-hidden">
                            <CardHeader className="pb-2">
                              <CardTitle className="text-base">{doc.title}</CardTitle>
                            </CardHeader>
                            <CardContent className="pt-0">
                              <p className="text-sm text-gray-500 mb-3">Category: {doc.category}</p>
                              <Button
                                variant="outline"
                                size="sm"
                                className="w-full flex items-center justify-center"
                                asChild
                              >
                                <a href={`/policies-and-procedures/${doc.filename}`} target="_blank" rel="noopener noreferrer">
                                  <FileText className="mr-2 h-4 w-4" />
                                  Download
                                </a>
                              </Button>
                            </CardContent>
                          </Card>
                        ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="examination">
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {policyDocuments
                        .filter(doc => doc.category === "Examination")
                        .map((doc, index) => (
                          <Card key={index} className="overflow-hidden">
                            <CardHeader className="pb-2">
                              <CardTitle className="text-base">{doc.title}</CardTitle>
                            </CardHeader>
                            <CardContent className="pt-0">
                              <p className="text-sm text-gray-500 mb-3">Category: {doc.category}</p>
                              <Button
                                variant="outline"
                                size="sm"
                                className="w-full flex items-center justify-center"
                                asChild
                              >
                                <a href={`/policies-and-procedures/${doc.filename}`} target="_blank" rel="noopener noreferrer">
                                  <FileText className="mr-2 h-4 w-4" />
                                  Download
                                </a>
                              </Button>
                            </CardContent>
                          </Card>
                        ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="registration">
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {policyDocuments
                        .filter(doc => doc.category === "Registration")
                        .map((doc, index) => (
                          <Card key={index} className="overflow-hidden">
                            <CardHeader className="pb-2">
                              <CardTitle className="text-base">{doc.title}</CardTitle>
                            </CardHeader>
                            <CardContent className="pt-0">
                              <p className="text-sm text-gray-500 mb-3">Category: {doc.category}</p>
                              <Button
                                variant="outline"
                                size="sm"
                                className="w-full flex items-center justify-center"
                                asChild
                              >
                                <a href={`/policies-and-procedures/${doc.filename}`} target="_blank" rel="noopener noreferrer">
                                  <FileText className="mr-2 h-4 w-4" />
                                  Download
                                </a>
                              </Button>
                            </CardContent>
                          </Card>
                        ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="administrative">
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {policyDocuments
                        .filter(doc => doc.category === "Administrative")
                        .map((doc, index) => (
                          <Card key={index} className="overflow-hidden">
                            <CardHeader className="pb-2">
                              <CardTitle className="text-base">{doc.title}</CardTitle>
                            </CardHeader>
                            <CardContent className="pt-0">
                              <p className="text-sm text-gray-500 mb-3">Category: {doc.category}</p>
                              <Button
                                variant="outline"
                                size="sm"
                                className="w-full flex items-center justify-center"
                                asChild
                              >
                                <a href={`/policies-and-procedures/${doc.filename}`} target="_blank" rel="noopener noreferrer">
                                  <FileText className="mr-2 h-4 w-4" />
                                  Download
                                </a>
                              </Button>
                            </CardContent>
                          </Card>
                        ))}
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Accreditations;
