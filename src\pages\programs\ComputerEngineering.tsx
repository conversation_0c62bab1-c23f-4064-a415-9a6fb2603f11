
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const ComputerEngineering = () => {
  const programData = {
    name: "Bachelor of Engineering in Computer Engineering",
    description: "Our Computer Engineering program combines electrical engineering and computer science to develop hardware and software systems. Students learn to design computer systems, networks, and embedded solutions.",
    duration: "4 years",
    faculty: "Faculty of Engineering",
    degreeAwarded: "Bachelor of Engineering (BEng) in Computer Engineering",
    careerOpportunities: [
      "Computer Hardware Engineer",
      "Embedded Systems Engineer",
      "FPGA Designer",
      "Network Engineer",
      "IoT Developer",
      "Systems Architect",
      "Firmware Engineer",
      "Cybersecurity Engineer"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Digital Logic Design",
          "Computer Programming",
          "Engineering Mathematics",
          "Electric Circuits",
          "Introduction to Computer Engineering"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Computer Organization",
          "Data Structures and Algorithms",
          "Electronic Circuits",
          "Discrete Mathematics",
          "Object-Oriented Programming"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Microprocessors and Microcontrollers",
          "Operating Systems",
          "Computer Networks",
          "Digital Signal Processing",
          "Embedded Systems"
        ]
      },
      {
        year: "Fourth Year",
        courses: [
          "VLSI Design",
          "Computer Architecture",
          "Real-Time Systems",
          "Capstone Design Project",
          "Professional Ethics"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Minimum GPA of 3.0",
      "Strong background in Mathematics and Computer Science",
      "Programming experience (preferred)",
      "English language proficiency",
      "Statement of purpose"
    ],
    programObjectives: [
      "Develop strong understanding of computer hardware and software integration",
      "Master design and analysis of computer systems",
      "Apply engineering knowledge to solve complex computing problems",
      "Use modern engineering tools and technologies effectively",
      "Understand professional and ethical responsibilities of engineers",
      "Prepare for careers in hardware design, embedded systems, and networking"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default ComputerEngineering;
