
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const Undergraduate = () => {
  const requirements = [
    {
      title: "Academic Requirements",
      items: [
        "High School Diploma or equivalent",
        "Minimum GPA of 2.5",
        "SAT or ACT scores (optional)",
        "English language proficiency test scores"
      ]
    },
    {
      title: "Required Documents",
      items: [
        "Completed application form",
        "Official transcripts",
        "Two letters of recommendation",
        "Personal statement",
        "Copy of passport or ID",
        "Recent photograph"
      ]
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">Undergraduate Admissions</h1>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {requirements.map((section, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{section.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="list-disc list-inside space-y-2">
                    {section.items.map((item, idx) => (
                      <li key={idx} className="text-gray-600">{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <Button asChild size="lg" className="mr-4">
              <Link to="/admissions/apply">Apply Now</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link to="/contact/request-info">Request Information</Link>
            </Button>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Undergraduate;
