
import React from 'react';

const stats = [
  { id: 1, value: '5,000+', label: 'Students' },
  { id: 2, value: '300+', label: 'Faculty Members' },
  { id: 3, value: '60+', label: 'Academic Programs' },
  { id: 4, value: '50+', label: 'Years of Excellence' },
  { id: 5, value: '30+', label: 'Research Centers' },
  { id: 6, value: '20,000+', label: 'Alumni Worldwide' }
];

const QuickFacts = () => {
  return (
    <section className="py-16 bg-aul-navy text-white">
      <div className="aul-container">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">AUL at a Glance</h2>
          <div className="h-1 w-20 bg-aul-gold mx-auto"></div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
          {stats.map((stat) => (
            <div key={stat.id} className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-aul-gold mb-2">{stat.value}</div>
              <div className="text-lg text-gray-300">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default QuickFacts;
