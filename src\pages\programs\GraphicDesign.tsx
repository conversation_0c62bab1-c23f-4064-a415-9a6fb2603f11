
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const GraphicDesign = () => {
  const programData = {
    name: "Bachelor of Arts in Graphic Design",
    description: "Our Graphic Design program combines artistic creativity with technical skills to prepare students for careers in visual communication. Students learn to create compelling designs across various media platforms.",
    duration: "4 years",
    faculty: "Faculty of Arts",
    degreeAwarded: "Bachelor of Arts (BA) in Graphic Design",
    careerOpportunities: [
      "Graphic Designer",
      "Art Director",
      "Brand Identity Designer",
      "Packaging Designer",
      "UI/UX Designer",
      "Web Designer",
      "Illustrator",
      "Publication Designer"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Design Fundamentals",
          "Drawing Techniques",
          "Color Theory",
          "Digital Imaging",
          "Typography Basics"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Advanced Typography",
          "Branding and Identity",
          "Web Design Fundamentals",
          "Illustration Techniques",
          "Design History"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Package Design",
          "Editorial Design",
          "User Experience Design",
          "Motion Graphics",
          "Interactive Design"
        ]
      },
      {
        year: "Fourth Year",
        courses: [
          "Advanced Design Studio",
          "Professional Practice",
          "Design Research Methods",
          "Portfolio Development",
          "Capstone Project"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Portfolio of creative work",
      "Minimum GPA of 2.5",
      "Demonstrated artistic ability",
      "English language proficiency",
      "Statement of purpose"
    ],
    programObjectives: [
      "Develop strong visual communication and design skills",
      "Master industry-standard design software and techniques",
      "Apply design thinking to solve visual communication problems",
      "Understand design principles and their application across media",
      "Develop professional portfolio showcasing diverse design work",
      "Prepare for careers in advertising, publishing, and digital media"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default GraphicDesign;
