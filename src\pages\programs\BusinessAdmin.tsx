
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const BusinessAdmin = () => {
  const programData = {
    name: "Bachelor of Business Administration",
    description: "The Business Administration program prepares future business leaders with a comprehensive understanding of modern business practices, management principles, and entrepreneurial skills.",
    duration: "3 years",
    faculty: "Faculty of Business",
    degreeAwarded: "Bachelor of Business Administration (BBA)",
    careerOpportunities: [
      "Business Manager",
      "Management Consultant",
      "Financial Analyst",
      "Marketing Manager",
      "Entrepreneur",
      "Human Resources Manager",
      "Operations Manager",
      "Business Development Executive"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Principles of Management",
          "Business Mathematics",
          "Economics for Business",
          "Business Communication",
          "Accounting Fundamentals"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Marketing Management",
          "Financial Management",
          "Human Resource Management",
          "Business Law",
          "Operations Management"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Strategic Management",
          "International Business",
          "Entrepreneurship",
          "Business Ethics",
          "Capstone Project"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Minimum GPA of 2.8",
      "Strong analytical and communication skills",
      "Leadership experience (preferred)",
      "English language proficiency",
      "Statement of purpose"
    ],
    programObjectives: [
      "Develop comprehensive business management skills",
      "Understanding of global business practices",
      "Build leadership and decision-making capabilities",
      "Master business analytics and problem-solving",
      "Develop entrepreneurial mindset",
      "Enhance communication and teamwork skills"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default BusinessAdmin;
