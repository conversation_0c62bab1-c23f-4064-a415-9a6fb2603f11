
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Open Sans', sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
}

.aul-container {
  width: 100%;
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* University colors */
:root {
  --aul-navy: #0A2240;
  --aul-gold: #CFAB5E;
  --aul-red: #B22234;
}

.bg-aul-navy {
  background-color: var(--aul-navy);
}

.bg-aul-gold {
  background-color: var(--aul-gold);
}

.bg-aul-red {
  background-color: var(--aul-red);
}

.text-aul-navy {
  color: var(--aul-navy);
}

.text-aul-gold {
  color: var(--aul-gold);
}

.text-aul-red {
  color: var(--aul-red);
}

.border-aul-navy {
  border-color: var(--aul-navy);
}

.border-aul-gold {
  border-color: var(--aul-gold);
}

.border-aul-red {
  border-color: var(--aul-red);
}

/* Animations */
.animate-fade-in {
  animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  0% { 
    opacity: 0;
    transform: translateY(20px);
  }
  100% { 
    opacity: 1;
    transform: translateY(0);
  }
}
