
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Accounting = () => {
  const programData = {
    name: "Bachelor of Science in Accounting",
    description: "Our Accounting program provides students with a strong foundation in accounting principles, auditing, taxation, and financial reporting. Students develop analytical skills to interpret financial information and make informed decisions.",
    duration: "3 years",
    faculty: "Faculty of Business",
    degreeAwarded: "Bachelor of Science (BSc) in Accounting",
    careerOpportunities: [
      "Certified Public Accountant",
      "Auditor",
      "Tax Consultant",
      "Financial Controller",
      "Management Accountant",
      "Forensic Accountant",
      "Budget Analyst",
      "Government Accountant"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Principles of Accounting",
          "Business Law",
          "Economics for Business",
          "Business Mathematics",
          "Business Communication"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Intermediate Accounting",
          "Cost Accounting",
          "Taxation",
          "Auditing Principles",
          "Business Statistics"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Advanced Accounting",
          "Financial Statement Analysis",
          "Accounting Information Systems",
          "International Accounting",
          "Capstone Project"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Minimum GPA of 2.8",
      "Strong analytical and quantitative skills",
      "Attention to detail",
      "English language proficiency",
      "Statement of purpose"
    ],
    programObjectives: [
      "Develop strong understanding of accounting principles and concepts",
      "Master financial statement preparation and analysis",
      "Apply accounting knowledge to solve complex business problems",
      "Understand auditing procedures and internal controls",
      "Develop ethical reasoning in accounting contexts",
      "Prepare for careers in public accounting, corporate finance, and government"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default Accounting;
